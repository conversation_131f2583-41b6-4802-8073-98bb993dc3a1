// components/ui/Modal.tsx

import React from 'react';
import {
  Modal as RNModal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { Spacing, FontSizes, FontWeights, BorderRadius } from '@/constants/Colors';
import Button from './Button';

const { width, height } = Dimensions.get('window');

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
}

export default function Modal({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  closeOnBackdrop = true,
  size = 'medium',
}: ModalProps) {
  const { colors } = useTheme();

  const getModalSize = () => {
    switch (size) {
      case 'small':
        return {
          width: Math.min(width * 0.8, 400),
          maxHeight: height * 0.6,
        };
      case 'large':
        return {
          width: Math.min(width * 0.95, 800),
          maxHeight: height * 0.9,
        };
      case 'fullscreen':
        return {
          width: width,
          height: height,
        };
      default: // medium
        return {
          width: Math.min(width * 0.9, 600),
          maxHeight: height * 0.8,
        };
    }
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: size === 'fullscreen' ? 0 : Spacing.lg,
    },
    modal: {
      backgroundColor: colors.background,
      borderRadius: size === 'fullscreen' ? 0 : BorderRadius.lg,
      overflow: 'hidden',
      ...getModalSize(),
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: Spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    title: {
      fontSize: FontSizes.lg,
      fontWeight: FontWeights.semibold,
      color: colors.text,
      flex: 1,
    },
    closeButton: {
      padding: Spacing.sm,
      marginLeft: Spacing.md,
    },
    closeButtonText: {
      fontSize: FontSizes.lg,
      color: colors.textSecondary,
    },
    content: {
      flex: 1,
      padding: Spacing.lg,
    },
  });

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modal}>
              {(title || showCloseButton) && (
                <View style={styles.header}>
                  {title && <Text style={styles.title}>{title}</Text>}
                  {showCloseButton && (
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={onClose}
                    >
                      <Text style={styles.closeButtonText}>✕</Text>
                    </TouchableOpacity>
                  )}
                </View>
              )}
              <View style={styles.content}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
}
