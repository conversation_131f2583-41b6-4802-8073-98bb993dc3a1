// components/ui/Badge.tsx

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

import { useTheme } from '@/hooks/useTheme';
import { Spacing, FontSizes, FontWeights, BorderRadius } from '@/constants/Colors';

export type BadgeVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeProps {
  text: string;
  variant?: BadgeVariant;
  size?: BadgeSize;
  style?: ViewStyle;
  textStyle?: TextStyle;
  customColor?: string;
}

export default function Badge({
  text,
  variant = 'primary',
  size = 'medium',
  style,
  textStyle,
  customColor,
}: BadgeProps) {
  const { colors } = useTheme();

  const getBackgroundColor = () => {
    if (customColor) return customColor;
    
    switch (variant) {
      case 'primary':
        return colors.primary;
      case 'secondary':
        return colors.secondary;
      case 'success':
        return colors.success;
      case 'warning':
        return colors.warning;
      case 'error':
        return colors.error;
      case 'info':
        return colors.info;
      default:
        return colors.primary;
    }
  };

  const getPadding = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: Spacing.sm,
          paddingVertical: Spacing.xs,
        };
      case 'large':
        return {
          paddingHorizontal: Spacing.lg,
          paddingVertical: Spacing.sm,
        };
      default: // medium
        return {
          paddingHorizontal: Spacing.md,
          paddingVertical: Spacing.xs,
        };
    }
  };

  const getFontSize = () => {
    switch (size) {
      case 'small':
        return FontSizes.xs;
      case 'large':
        return FontSizes.md;
      default: // medium
        return FontSizes.sm;
    }
  };

  const styles = StyleSheet.create({
    badge: {
      backgroundColor: getBackgroundColor(),
      borderRadius: BorderRadius.round,
      alignSelf: 'flex-start',
      ...getPadding(),
    },
    text: {
      fontSize: getFontSize(),
      fontWeight: FontWeights.medium,
      color: '#FFFFFF',
      textAlign: 'center',
    },
  });

  return (
    <View style={[styles.badge, style]}>
      <Text style={[styles.text, textStyle]}>{text}</Text>
    </View>
  );
}
